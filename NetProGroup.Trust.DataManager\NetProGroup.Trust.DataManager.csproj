﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
  </PropertyGroup>

	<PropertyGroup>
		<NoWarn>$(NoWarn);CA1014;CA1716</NoWarn>
		<!-- CA1014 == CLS Compliancy, not required -->
		<!-- CA1716 == Don't use Shared keyword in namespace. -->
	</PropertyGroup>

  <ItemGroup>
    <None Remove="stylecop.json" />
  </ItemGroup>

  <ItemGroup>
    <AdditionalFiles Include="stylecop.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="ClosedXML" Version="0.104.1" />
    <PackageReference Include="EFCore.BulkExtensions" Version="8.1.1" />
    <PackageReference Include="Microsoft.Kiota.Abstractions" Version="1.9.9" />
    <PackageReference Include="Microsoft.Kiota.Authentication.Azure" Version="1.9.9" />
    <PackageReference Include="Microsoft.Kiota.Http.HttpClientLibrary" Version="1.9.9" />
    <PackageReference Include="Microsoft.Kiota.Serialization.Form" Version="1.9.9" />
    <PackageReference Include="Microsoft.Kiota.Serialization.Json" Version="1.9.9" />
    <PackageReference Include="Microsoft.Kiota.Serialization.Text" Version="1.9.9" />
    <PackageReference Include="NetPro.StyleCop.Configuration.Package" Version="1.1.24" />
    <PackageReference Include="NetProGroup.Framework" Version="1.4.5" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.1.118">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="TwoFactorAuth.Net" Version="1.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\NetProGroup.Trust.Application.Contracts\NetProGroup.Trust.Application.Contracts.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Domain\NetProGroup.Trust.Domain.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Payment\NetProGroup.Trust.Payment.csproj" />
    <ProjectReference Include="..\NetProGroup.Trust.Repository\NetProGroup.Trust.Domain.Repository.csproj" />
  </ItemGroup>


  <ItemGroup>
    <EmbeddedResource Include="Resources\Reports\companies-str-submission-status.xlsx" />
    <EmbeddedResource Include="Resources\Reports\contacts-info.xlsx" />
    <EmbeddedResource Include="Resources\Reports\economic-substance-report-submissions.xlsx" />
	  <EmbeddedResource Include="Resources\Reports\financial-report.xlsx" />
    <EmbeddedResource Include="Resources\Reports\submissions-not-paid.xlsx" />
    <EmbeddedResource Include="Resources\ES_BAHAMAS\_EconomicSubstanceBahamas_Bahamas.xlsx" />
    <EmbeddedResource Include="Resources\STR_NEVIS\2019_SimplifiedTaxReturn_Nevis.xlsx" />
    <EmbeddedResource Include="Resources\STR_NEVIS\2020_SimplifiedTaxReturn_Nevis.xlsx" />
    <EmbeddedResource Include="Resources\STR_NEVIS\2021_SimplifiedTaxReturn_Nevis.xlsx" />
    <EmbeddedResource Include="Resources\STR_NEVIS\2022_SimplifiedTaxReturn_Nevis.xlsx" />
    <EmbeddedResource Include="Resources\STR_NEVIS\2023_SimplifiedTaxReturn_Nevis.xlsx" />
	<EmbeddedResource Include="Resources\STR_NEVIS\2024_SimplifiedTaxReturn_Nevis.xlsx" />
    <EmbeddedResource Include="Resources\Reports\basic-financial-report-submissions.xlsx" />
    <EmbeddedResource Include="Resources\Reports\submission-data.xlsx" />
    <EmbeddedResource Include="Resources\Reports\simplified-tax-return.xlsx" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Payments\Provider\" />
  </ItemGroup>

</Project>
