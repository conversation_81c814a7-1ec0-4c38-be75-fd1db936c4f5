// <copyright file="OutsourcingRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.Bahamas.Helpers;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Populate a row for the IRD submission report.
    /// </summary>
    public class OutsourcingRowPopulator : LinePopulatorBase, IOutsourcingRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Retrieve the selected relevant activities
            var relevantActivities = data.FormDocument.Attributes.GetAttributesWithPrefix(WellKnownFormDocumentAttibuteKeys.RelevantActivities).ToList();

            // Group the relevant activities
            var relevantActivityGroups = relevantActivities.GroupBy(a => a.Key.Split(WellKnownFormDocumentAttibuteKeys.RelevantActivities)[1].Split(".")[0]);

            foreach (var relevantActivityGroup in relevantActivityGroups)
            {
                // Check if the activity was selected
                var isSelected = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Selected);

                if (isSelected == "true")
                {
                    // Retrieve the name
                    var relevantActivity = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Label);

                    // Check the selected activity
                    string relevantActivityKey = BahamasReportHelperMethods.RetrieveRelevantActivityKey(relevantActivity);

                    // Retrieve the relevant activity data
                    var relevantActivityData = data.FormDocument.Attributes.GetAttributesWithPrefix(relevantActivityKey).ToList();

                    // Retrieve the created outsourcing providers
                    var outsourcingProviders = relevantActivityData.GetAttributesWithPrefix(WellKnownFormDocumentAttibuteKeys.OutsourcingProviders).ToList();

                    // Group the providers
                    var outsourcingProviderGroups = outsourcingProviders.GroupBy(
                        a => a.Key.Split(relevantActivityKey + WellKnownFormDocumentAttibuteKeys.OutsourcingProviders)[1].Split(".")[0]);

                    foreach (var outsourcingProviderGroup in outsourcingProviderGroups)
                    {
                        // Retrieve the entity unique id
                        var entityUniqueId = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.EntityId);

                        SetCellValueAndStyle(worksheet, currentRow, 1, entityUniqueId);

                        // Retrieve the name
                        var activityName = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Label);

                        SetCellValueAndStyle(worksheet, currentRow, 2, activityName);

                        var financialPeriodEndDate = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.FinancialPeriodEndAt);

                        SetCellValueAndStyle(worksheet, currentRow, 3, financialPeriodEndDate);

                        // Retrieve the provider's name
                        var providerName = outsourcingProviderGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.EntityName);

                        SetCellValueAndStyle(worksheet, currentRow, 4, providerName);

                        // Retrieve the details of resources deployed
                        var providerDetails = outsourcingProviderGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.DetailsOfResources);

                        SetCellValueAndStyle(worksheet, currentRow, 5, providerDetails);

                        // Retrieve the number of staff
                        var numberOfStaff = outsourcingProviderGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.NumberOfStaff);

                        SetCellValueAndStyle(worksheet, currentRow, 6, numberOfStaff);

                        // Retrieve if the entity is able to monitor and control the activity
                        var monitoringAndControl = outsourcingProviderGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.MonitoringAndControl);

                        SetCellValueAndStyle(worksheet, currentRow, 7, monitoringAndControl);

                        // Retrieve the monitor and control explanation
                        var monitoringControlExplanation = outsourcingProviderGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.MonitoringControlExplanation);

                        SetCellValueAndStyle(worksheet, currentRow, 8, monitoringControlExplanation);

                        // Retrieve the physical address
                        var physicalAddress = outsourcingProviderGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.PhysicalAddress);

                        SetCellValueAndStyle(worksheet, currentRow, 9, physicalAddress);

                        currentRow += 1;
                    }
                }
            }
        }
    }
}