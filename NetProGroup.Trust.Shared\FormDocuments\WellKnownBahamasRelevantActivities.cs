// <copyright file="WellKnownBahamasRelevantActivities.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Shared.FormDocuments
{
    /// <summary>
    /// A list of known relevant activity names for Bahamas.
    /// </summary>
    public static class WellKnownBahamasRelevantActivities
    {
        /// <summary>
        /// The relevant activity is 'None'.
        /// </summary>
        public const string None = "None";

        /// <summary>
        /// The relevant activity is 'Holding Business'.
        /// </summary>
        public const string HoldingBusiness = "Holding Business";

        /// <summary>
        /// The relevant activity is 'Finance and Leasing Business'.
        /// </summary>
        public const string FinanceLeasingBusiness = "Finance and Leasing Business";

        /// <summary>
        /// The relevant activity is 'Banking Business'.
        /// </summary>
        public const string BankingBusiness = "Banking Business";

        /// <summary>
        /// The relevant activity is 'Insurance Business'.
        /// </summary>
        public const string InsuranceBusiness = "Insurance Business";

        /// <summary>
        /// The relevant activity is 'Fund Management Business'.
        /// </summary>
        public const string FundManagementBusiness = "Fund Management Business";

        /// <summary>
        /// The relevant activity is 'Headquarters Business'.
        /// </summary>
        public const string HeadquartersBusiness = "Headquarters Business";

        /// <summary>
        /// The relevant activity is 'Shipping Business'.
        /// </summary>
        public const string ShippingBusiness = "Shipping Business";

        /// <summary>
        /// The relevant activity is 'Intellectual Propertry Business'.
        /// </summary>
        public const string IntellectualPropertryBusiness = "Intellectual Propertry Business";
    }
}