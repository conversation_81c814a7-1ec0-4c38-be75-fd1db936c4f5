using NetProGroup.Trust.Shared.FormDocuments;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace NetProGroup.Trust.Reports.Bahamas.Helpers
{
    /// <summary>
    /// Setup helper method for Bahamas reports.
    /// </summary>
    public static class BahamasReportHelperMethods
    {
        /// <summary>
        /// Retrieves the relevant activity key based on the name.
        /// </summary>
        /// <param name="relevantActivity">The relevant activity name as WellKnownBahamasRelevantActivities.</param>
        /// <returns>The relevant activity key as WellKnownBahamasRelevantActivityKeys.</returns>
        public static string RetrieveRelevantActivityKey(string relevantActivity)
        {
            var dictionary = new Dictionary<string, string>
            {
                { WellKnownBahamasRelevantActivities.HoldingBusiness , WellKnownFormDocumentAttibuteKeys.HoldingBusiness },
                { WellKnownBahamasRelevantActivities.FinanceLeasingBusiness , WellKnownFormDocumentAttibuteKeys.FinanceLeasingBusiness },
                { WellKnownBahamasRelevantActivities.BankingBusiness , WellKnownFormDocumentAttibuteKeys.BankingBusiness },
                { WellKnownBahamasRelevantActivities.InsuranceBusiness , WellKnownFormDocumentAttibuteKeys.InsuranceBusiness },
                { WellKnownBahamasRelevantActivities.FundManagementBusiness , WellKnownFormDocumentAttibuteKeys.FundManagementBusiness },
                { WellKnownBahamasRelevantActivities.HeadquartersBusiness , WellKnownFormDocumentAttibuteKeys.HeadquartersBusiness },
                { WellKnownBahamasRelevantActivities.ShippingBusiness , WellKnownFormDocumentAttibuteKeys.ShippingBusiness },
                { WellKnownBahamasRelevantActivities.IntellectualPropertryBusiness, WellKnownFormDocumentAttibuteKeys.IntellectualPropertryBusiness }
            };

            return dictionary.TryGetValue(relevantActivity, out var value) ? value : "";
        }

    }
}