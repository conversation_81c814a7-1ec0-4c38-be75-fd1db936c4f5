// <copyright file="DeclarationRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Reports.Bahamas.Helpers;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Populate a row for the IRD submission report.
    /// </summary>
    public class DeclarationRowPopulator : LinePopulatorBase, IDeclarationRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            var form = data.FormDocument.FormDocumentRevisions.OrderBy(r => r.Revision).LastOrDefault()?.GetFormBuilder().Form as KeyValueForm;

            // Retrieve the entity unique id
            SetCellValueAndStyle(worksheet, currentRow, 1, GetValueOrDefault(form, FormKeys.EntityDetailsEntityId));

            // Retrieve the entity taxpayer id
            SetCellValueAndStyle(worksheet, currentRow, 2, GetValueOrDefault(form, FormKeys.EntityDetailsTin));

            // Retrieve if the physical business address is the same as the registered address
            SetCellValueAndStyle(worksheet, currentRow, 3, GetValueOrDefault(form, FormKeys.EntityDetailsSameAddress));

            // Retrieve the address line 1
            SetCellValueAndStyle(worksheet, currentRow, 4, GetValueOrDefault(form, FormKeys.EntityAddress));

            // Retrieve the address apt or unit
            SetCellValueAndStyle(worksheet, currentRow, 5, GetValueOrDefault(form, FormKeys.EntityAptUnit));

            // Retrieve the address country code
            SetCellValueAndStyle(worksheet, currentRow, 6, GetValueOrDefault(form, FormKeys.EntityCountry));

            // Retrieve if an application has been made and confirmed with the Ministry of Finance to change the financial period
            var isApplicationMadeAndConfirmed = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.IsFirstFinancialReport);

            SetCellValueAndStyle(worksheet, currentRow, 7, isApplicationMadeAndConfirmed);

            // Retrieve the financial period
            var financialPeriodStartDate = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.FinancialPeriodStartAt);

            SetCellValueAndStyle(worksheet, currentRow, 8, financialPeriodStartDate);

            var financialPeriodEndDate = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.FinancialPeriodEndAt);

            SetCellValueAndStyle(worksheet, currentRow, 9, financialPeriodEndDate);

            // Retrieve if the entity is subject to reclassification
            var isSubjectToReclassification = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.IsReclassifiedToPEH);

            SetCellValueAndStyle(worksheet, currentRow, 10, isSubjectToReclassification);


            // Retrieve the selected relevant activities
            var relevantActivities = data.FormDocument.Attributes.GetAttributesWithPrefix(WellKnownFormDocumentAttibuteKeys.RelevantActivities).ToList();

            // Group the relevant activities
            var relevantActivityGroups = relevantActivities.GroupBy(a => a.Key.Split(WellKnownFormDocumentAttibuteKeys.RelevantActivities)[1].Split(".")[0]);

            int currentIndex = 0;

            var selectedRelevantActivities = new List<string>();

            foreach (var relevantActivityGroup in relevantActivityGroups)
            {
                // Check if the activity was selected
                var isSelected = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Selected);

                if (isSelected == "true")
                {
                    // Retrieve the name
                    var activityName = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Label);

                    SetCellValueAndStyle(worksheet, currentRow + currentIndex, 11, activityName);

                    // Store the relevant activity label
                    selectedRelevantActivities.Add(activityName);

                    // Check if the activity was carried for a partial part of the financial period
                    var carriedForOnlyPartOfFinancialPeriod = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.CarriedOnForOnlyPartOfFinancialPeriod);

                    SetCellValueAndStyle(worksheet, currentRow + currentIndex, 12, carriedForOnlyPartOfFinancialPeriod);

                    // Retrieve the activity period
                    var activityStartDate = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.StartDate);

                    SetCellValueAndStyle(worksheet, currentRow + currentIndex, 13, activityStartDate);

                    var activityEndDate = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.EndDate);

                    SetCellValueAndStyle(worksheet, currentRow + currentIndex, 14, activityEndDate);

                    currentIndex += 1;
                }
            }

            // Retrieve if the user is a 100% Bahamian 
            var isBahamianResident = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.IsBahamianResident);

            SetCellValueAndStyle(worksheet, currentRow, 15, isBahamianResident);

            // Retrieve if the user is an investment fund
            var isInvestmentFund = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.IsInvestmentFund);

            SetCellValueAndStyle(worksheet, currentRow, 16, isInvestmentFund);

            // Retrieve the annual gross income
            var entityGrossTotalAnnualIncomeAmount = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.EntityGrossTotalAnnualIncomeAmount);

            SetCellValueAndStyle(worksheet, currentRow, 17, entityGrossTotalAnnualIncomeAmount);

            // Retrieve the entity is part of the MNE group
            var isPartOfMneGroup = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.IsPartOfMneGroup);

            SetCellValueAndStyle(worksheet, currentRow, 18, isPartOfMneGroup);

            // Retrieve the MNE group name
            var mneGroupName = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.MneGroupName);

            SetCellValueAndStyle(worksheet, currentRow, 19, mneGroupName);

            // Retrieve if the entity intend to make a tax residency claim outside Bahamas
            var intendsToClaimTaxResidencyOutsideBahamas = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.IntendsToClaimTaxResidencyOutsideBahamas);

            SetCellValueAndStyle(worksheet, currentRow, 20, intendsToClaimTaxResidencyOutsideBahamas);

            // Retrieve the jurisdiction selected
            var taxResidencyJurisdiction = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.TaxResidencyJurisdiction);

            SetCellValueAndStyle(worksheet, currentRow, 21, taxResidencyJurisdiction);

            // Retrieve the tax payer identification number
            var taxPayerIdentificationNumber = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.TaxPayerIdentificationNumber);

            SetCellValueAndStyle(worksheet, currentRow, 22, taxPayerIdentificationNumber);

            // Retrieve if the entity has ultimate parent entity
            var hasUltimateParentEntity = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.HasUltimateParentEntity);

            SetCellValueAndStyle(worksheet, currentRow, 23, hasUltimateParentEntity);

            // Retrieve if the entity has immediate parent entity
            var hasImmediateParentEntity = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.HasImmediateParentEntity);

            SetCellValueAndStyle(worksheet, currentRow, 24, hasImmediateParentEntity);

            int auxIndex = 0;

            // Retrieve the relevant activities details
            foreach (var relevantActivity in selectedRelevantActivities)
            {
                // Check the selected activity
                string relevantActivityKey = BahamasReportHelperMethods.RetrieveRelevantActivityKey(relevantActivity);

                // Retrieve the relevant activity data
                var relevantActivityData = data.FormDocument.Attributes.GetAttributesWithPrefix(relevantActivityKey).ToList();

                // Retrieve the gross income
                var grossIncome = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.TotalGrossIncome);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 25, grossIncome);

                // Retrieve the net book values
                var netBookValuesAssets = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.NetBookValuesAssets);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 26, netBookValuesAssets);

                // Retrieve the asset description
                var assetsDescriptionBahamas = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.AssetsDescriptionBahamas);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 27, assetsDescriptionBahamas);

                // Retrieve if the activity is directed and managed in Bahamas
                var isDirectedAndManagedInBahamas = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.IsDirectedAndManagedInBahamas);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 28, isDirectedAndManagedInBahamas);

                // Retrieve the number of board meetings
                var numberOfMeetings = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.NumberOfMeetings);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 29, numberOfMeetings);

                // Retrieve the number of board meetings in Bahamas
                var numberOfMeetingsInBahamas = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.NumberOfMeetingsInBahamas);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 30, numberOfMeetingsInBahamas);

                // Retrieve the Quorum for board meetings
                var quorumDirectors = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.QuorumDirectors);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 31, quorumDirectors);

                // Retrieve if any of the meeting held in bahamas the quorum was physically present
                var quorumPhysicallyPresent = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.QuorumPhysicallyPresent);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 32, quorumPhysicallyPresent);

                // Retrieve if the minutes were kept in bahamas
                var areMinutesKeptInBahamas = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.AreMinutesKeptInBahamas);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 33, areMinutesKeptInBahamas);

                // Retrieve the total expenditure
                var totalExpenditureRelevantActivity = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.TotalExpenditureRelevantActivity);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 34, totalExpenditureRelevantActivity);

                // Retrieve the total bahamas expenditure
                var totalExpenditureBahamas = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.TotalExpenditureBahamas);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 35, totalExpenditureBahamas);

                // Retrieve the total of employees
                var totalEmployeesEntity = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.TotalEmployeesEntity);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 36, totalEmployeesEntity);

                // Retrieve the total of employees for the relevant activity
                var totalEmployeesRelevantActivity = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.TotalEmployeesRelevantActivity);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 37, totalEmployeesRelevantActivity);

                // Retrieve the total of employees for the relevant activity present in Bahamas
                var totalEmployeesBahamas = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.TotalEmployeesBahamas);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 38, totalEmployeesBahamas);

                // Retrieve if there is any CIGA
                var hasCiga = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.HasCiga);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 39, hasCiga);

                // Retrieve the CIGA income
                var cigaOutsourcingProportion = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.CigaOutsourcingProportion);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 40, cigaOutsourcingProportion);

                // Retrieve the CIGA expenditure
                var bahamasOutsourcingExpenditure = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.BahamasOutsourcingExpenditure);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 41, bahamasOutsourcingExpenditure);

                // Retrieve if the company comply with Bahamas laws and regulations
                var isCompliantWithBahamasLawsAndRegulations = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.IsCompliantWithBahamasLawsAndRegulations);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 42, isCompliantWithBahamasLawsAndRegulations);

                // Retrieve if the company is a high-risk intellectual property
                var isHighRiskIpEntity = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.IsHighRiskIpEntity);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 43, isHighRiskIpEntity);

                // Retrieve the relevant IP asset
                var relevantIpAsset = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.RelevantIpAsset);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 44, relevantIpAsset);

                // Retrieve the IP explanation
                var incomeGenerationExplanation = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.IncomeGenerationExplanation);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 45, incomeGenerationExplanation);

                // Retrieve the employee responsibility
                var employeeResponsibility = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.EmployeeResponsibility);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 46, employeeResponsibility);

                // Retrieve the nature and history of the strategic decisions
                var strategicDecisionsBahamas = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.StrategicDecisionsBahamas);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 47, strategicDecisionsBahamas);

                // Retrieve the nature and history of the trading activities
                var tradingActivitiesBahamas = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.TradingActivitiesBahamas);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 48, tradingActivitiesBahamas);

                // Retrieve the gross income through royalties
                var grossIncomeRoyalties = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.GrossIncomeRoyalties);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 49, grossIncomeRoyalties);

                // Retrieve the gross income through sales
                var grossIncomeSaleIpAsset = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.GrossIncomeSaleIpAsset);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 50, grossIncomeSaleIpAsset);

                // Retrieve the gross income through others
                var grossIncomeOtherSources = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.GrossIncomeOtherSources);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 51, grossIncomeOtherSources);

                // Retrieve the detailed business plans
                var businessPlanExplanation = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.BusinessPlanExplanation);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 52, businessPlanExplanation);

                // Retrieve the concrete decision making evidence
                var decisionMakingEvidenceExplanation = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.DecisionMakingEvidenceExplanation);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 53, decisionMakingEvidenceExplanation);

                // Retrieve the other facts
                var additionalComplianceExplanation = relevantActivityData.GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.AdditionalComplianceExplanation);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 54, additionalComplianceExplanation);

                auxIndex += 1;
            }

            // Retrieve the additionsl supporting comments
            var additionalComments = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.AdditionalComments);

            SetCellValueAndStyle(worksheet, currentRow, 55, additionalComments);

            currentRow += auxIndex;
        }
    }
}