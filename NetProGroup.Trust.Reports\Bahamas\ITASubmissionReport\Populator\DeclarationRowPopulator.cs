// <copyright file="DeclarationRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Reports.Bahamas.Helpers;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Populate a row for the IRD submission report.
    /// </summary>
    public class DeclarationRowPopulator : LinePopulatorBase, IDeclarationRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            var form = data.FormDocument.FormDocumentRevisions.OrderBy(r => r.Revision).LastOrDefault()?.GetFormBuilder().Form as KeyValueForm;

            // Retrieve the entity unique id
            SetCellValueAndStyle(worksheet, currentRow, 1, GetValueOrDefault(form, FormKeys.EntityDetailsEntityId));

            // Retrieve the entity taxpayer id
            SetCellValueAndStyle(worksheet, currentRow, 2, GetValueOrDefault(form, FormKeys.EntityDetailsTin));

            // Retrieve if the physical business address is the same as the registered address
            SetCellValueAndStyle(worksheet, currentRow, 3, GetValueOrDefault(form, FormKeys.EntityDetailsSameAddress));

            // Retrieve the address line 1
            SetCellValueAndStyle(worksheet, currentRow, 4, GetValueOrDefault(form, FormKeys.EntityAddress));

            // Retrieve the address apt or unit
            SetCellValueAndStyle(worksheet, currentRow, 5, GetValueOrDefault(form, FormKeys.EntityAptUnit));

            // Retrieve the address country code
            SetCellValueAndStyle(worksheet, currentRow, 6, GetValueOrDefault(form, FormKeys.EntityCountry));

            // Retrieve if an application has been made and confirmed with the Ministry of Finance to change the financial period
            SetCellValueAndStyle(worksheet, currentRow, 7, GetValueOrDefault(form, FormKeys.FinancialPeriodDetailsFirstFinancialReport));

            // Retrieve the financial period
            SetCellValueAndStyle(worksheet, currentRow, 8, GetValueOrDefault(form, FormKeys.FinancialPeriodDetailsStartDate));

            SetCellValueAndStyle(worksheet, currentRow, 9, GetValueOrDefault(form, FormKeys.FinancialPeriodDetailsEndDate));

            // Retrieve if the entity is subject to reclassification
            SetCellValueAndStyle(worksheet, currentRow, 10, GetValueOrDefault(form, FormKeys.FinancialPeriodDetailsIsReclassifiedToPEH));


            // Retrieve the selected relevant activities
            var relevantActivities = data.FormDocument.Attributes.GetAttributesWithPrefix(FormKeys.RelevantActivitiesPrefix).ToList();

            // Group the relevant activities
            var relevantActivityGroups = relevantActivities.GroupBy(a => a.Key.Split(FormKeys.RelevantActivitiesPrefix)[1].Split(".")[0]);

            int currentIndex = 0;

            var selectedRelevantActivities = new List<string>();

            foreach (var relevantActivityGroup in relevantActivityGroups)
            {
                // Check if the activity was selected
                var isSelected = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(FormKeys.RelevantActivitiesSelectedSuffix);

                if (isSelected == "true")
                {
                    // Retrieve the name
                    var activityName = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(FormKeys.RelevantActivitiesLabelSuffix);

                    SetCellValueAndStyle(worksheet, currentRow + currentIndex, 11, activityName);

                    // Store the relevant activity label
                    selectedRelevantActivities.Add(activityName);

                    // Check if the activity was carried for a partial part of the financial period
                    var carriedForOnlyPartOfFinancialPeriod = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(FormKeys.RelevantActivitiesCarriedOnForOnlyPartOfFinancialPeriodSuffix);

                    SetCellValueAndStyle(worksheet, currentRow + currentIndex, 12, carriedForOnlyPartOfFinancialPeriod);

                    // Retrieve the activity period
                    var activityStartDate = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(FormKeys.RelevantActivitiesStartDateSuffix);

                    SetCellValueAndStyle(worksheet, currentRow + currentIndex, 13, activityStartDate);

                    var activityEndDate = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(FormKeys.RelevantActivitiesEndDateSuffix);

                    SetCellValueAndStyle(worksheet, currentRow + currentIndex, 14, activityEndDate);

                    currentIndex += 1;
                }
            }

            // Retrieve if the user is a 100% Bahamian
            SetCellValueAndStyle(worksheet, currentRow, 15, GetValueOrDefault(form, FormKeys.EntityDetailsIsBahamianResident));

            // Retrieve if the user is an investment fund
            SetCellValueAndStyle(worksheet, currentRow, 16, GetValueOrDefault(form, FormKeys.EntityDetailsIsInvestmentFund));

            // Retrieve the annual gross income
            SetCellValueAndStyle(worksheet, currentRow, 17, GetValueOrDefault(form, FormKeys.EntityDetailsTotalAnnualGross));

            // Retrieve the entity is part of the MNE group
            SetCellValueAndStyle(worksheet, currentRow, 18, GetValueOrDefault(form, FormKeys.EntityDetailsIsPartOfMneGroup));

            // Retrieve the MNE group name
            SetCellValueAndStyle(worksheet, currentRow, 19, GetValueOrDefault(form, FormKeys.EntityDetailsNameOfNmeGroup));

            // Retrieve if the entity intend to make a tax residency claim outside Bahamas
            SetCellValueAndStyle(worksheet, currentRow, 20, GetValueOrDefault(form, FormKeys.EntityDetailsIntendsToClaimTaxResidencyOutsideBahamas));

            // Retrieve the jurisdiction selected
            SetCellValueAndStyle(worksheet, currentRow, 21, GetValueOrDefault(form, FormKeys.EntityDetailsTaxResidencyJurisdiction));

            // Retrieve the tax payer identification number
            SetCellValueAndStyle(worksheet, currentRow, 22, GetValueOrDefault(form, FormKeys.EntityDetailsTaxPayerIdentificationNumber));

            // Retrieve if the entity has ultimate parent entity
            SetCellValueAndStyle(worksheet, currentRow, 23, GetValueOrDefault(form, FormKeys.EntityDetailsHasUltimateParentEntity));

            // Retrieve if the entity has immediate parent entity
            SetCellValueAndStyle(worksheet, currentRow, 24, GetValueOrDefault(form, FormKeys.EntityDetailsHasImmediateParentEntity));

            int auxIndex = 0;

            // Retrieve the relevant activities details
            foreach (var relevantActivity in selectedRelevantActivities)
            {
                // Check the selected activity
                string relevantActivityKey = BahamasReportHelperMethods.RetrieveRelevantActivityKey(relevantActivity);

                // Retrieve the relevant activity data
                var relevantActivityData = data.FormDocument.Attributes.GetAttributesWithPrefix(relevantActivityKey).ToList();

                // Retrieve the gross income
                var grossIncome = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessTotalGrossIncome);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 25, grossIncome);

                // Retrieve the net book values
                var netBookValuesAssets = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessNetBookValuesAssets);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 26, netBookValuesAssets);

                // Retrieve the asset description
                var assetsDescriptionBahamas = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessAssetsDescriptionBahamas);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 27, assetsDescriptionBahamas);

                // Retrieve if the activity is directed and managed in Bahamas
                var isDirectedAndManagedInBahamas = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessIsDirectedAndManagedInBahamas);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 28, isDirectedAndManagedInBahamas);

                // Retrieve the number of board meetings
                var numberOfMeetings = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessNumberOfMeetings);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 29, numberOfMeetings);

                // Retrieve the number of board meetings in Bahamas
                var numberOfMeetingsInBahamas = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessNumberOfMeetingsInBahamas);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 30, numberOfMeetingsInBahamas);

                // Retrieve the Quorum for board meetings
                var quorumDirectors = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessQuorumDirectors);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 31, quorumDirectors);

                // Retrieve if any of the meeting held in bahamas the quorum was physically present
                var quorumPhysicallyPresent = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessQuorumPhysicallyPresent);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 32, quorumPhysicallyPresent);

                // Retrieve if the minutes were kept in bahamas
                var areMinutesKeptInBahamas = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessAreMinutesKeptInBahamas);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 33, areMinutesKeptInBahamas);

                // Retrieve the total expenditure
                var totalExpenditureRelevantActivity = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessTotalExpenditureRelevantActivity);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 34, totalExpenditureRelevantActivity);

                // Retrieve the total bahamas expenditure
                var totalExpenditureBahamas = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessTotalExpenditureBahamas);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 35, totalExpenditureBahamas);

                // Retrieve the total of employees
                var totalEmployeesEntity = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessTotalEmployeesEntity);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 36, totalEmployeesEntity);

                // Retrieve the total of employees for the relevant activity
                var totalEmployeesRelevantActivity = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessTotalEmployeesRelevantActivity);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 37, totalEmployeesRelevantActivity);

                // Retrieve the total of employees for the relevant activity present in Bahamas
                var totalEmployeesBahamas = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessTotalEmployeesBahamas);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 38, totalEmployeesBahamas);

                // Retrieve if there is any CIGA
                var hasCiga = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessHasCiga);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 39, hasCiga);

                // Retrieve the CIGA income
                var cigaOutsourcingProportion = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessCigaOutsourcingProportion);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 40, cigaOutsourcingProportion);

                // Retrieve the CIGA expenditure
                var bahamasOutsourcingExpenditure = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessBahamasOutsourcingExpenditure);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 41, bahamasOutsourcingExpenditure);

                // Retrieve if the company comply with Bahamas laws and regulations
                var isCompliantWithBahamasLawsAndRegulations = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessIsCompliantWithBahamasLawsAndRegulations);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 42, isCompliantWithBahamasLawsAndRegulations);

                // Retrieve if the company is a high-risk intellectual property
                var isHighRiskIpEntity = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessIsHighRiskIpEntity);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 43, isHighRiskIpEntity);

                // Retrieve the relevant IP asset
                var relevantIpAsset = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessRelevantIpAsset);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 44, relevantIpAsset);

                // Retrieve the IP explanation
                var incomeGenerationExplanation = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessIncomeGenerationExplanation);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 45, incomeGenerationExplanation);

                // Retrieve the employee responsibility
                var employeeResponsibility = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessEmployeeResponsibility);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 46, employeeResponsibility);

                // Retrieve the nature and history of the strategic decisions
                var strategicDecisionsBahamas = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessStrategicDecisionsBahamas);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 47, strategicDecisionsBahamas);

                // Retrieve the nature and history of the trading activities
                var tradingActivitiesBahamas = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessTradingActivitiesBahamas);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 48, tradingActivitiesBahamas);

                // Retrieve the gross income through royalties
                var grossIncomeRoyalties = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessGrossIncomeRoyalties);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 49, grossIncomeRoyalties);

                // Retrieve the gross income through sales
                var grossIncomeSaleIpAsset = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessGrossIncomeSaleIpAsset);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 50, grossIncomeSaleIpAsset);

                // Retrieve the gross income through others
                var grossIncomeOtherSources = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessGrossIncomeOtherSources);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 51, grossIncomeOtherSources);

                // Retrieve the detailed business plans
                var businessPlanExplanation = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessBusinessPlanExplanation);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 52, businessPlanExplanation);

                // Retrieve the concrete decision making evidence
                var decisionMakingEvidenceExplanation = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessDecisionMakingEvidenceExplanation);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 53, decisionMakingEvidenceExplanation);

                // Retrieve the other facts
                var additionalComplianceExplanation = relevantActivityData.GetAttributeValueWithSuffix<string>(FormKeys.BusinessAdditionalComplianceExplanation);

                SetCellValueAndStyle(worksheet, currentRow + auxIndex, 54, additionalComplianceExplanation);

                auxIndex += 1;
            }

            // Retrieve the additionsl supporting comments
            SetCellValueAndStyle(worksheet, currentRow, 55, GetValueOrDefault(form, FormKeys.SupportingDetailsAdditionalComments));

            currentRow += auxIndex;
        }
    }
}