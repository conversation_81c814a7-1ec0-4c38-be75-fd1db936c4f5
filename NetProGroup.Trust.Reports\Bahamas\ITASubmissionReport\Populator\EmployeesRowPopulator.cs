// <copyright file="EmployeesRowPopulator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using ClosedXML.Excel;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Reports.Bahamas.Helpers;
using NetProGroup.Trust.Reports.ExcelTemplateExporter.Populator;
using NetProGroup.Trust.Shared.FormDocuments;

namespace NetProGroup.Trust.Reports.Bahamas.ITASubmissionReport.Populator
{
    /// <summary>
    /// Populate a row for the IRD submission report.
    /// </summary>
    public class EmployeesRowPopulator : LinePopulatorBase, IEmployeesRowPopulator
    {
        /// <inheritdoc />
        protected override XLColor FontColor => XLColor.Black;

        /// <inheritdoc />
        protected override double FontSize => 12;

        /// <inheritdoc />
        protected override string FontName => "Calibri";

        /// <inheritdoc />
        public void PopulateRow(IXLWorksheet worksheet, int currentRow, Submission data)
        {
            ArgumentNullException.ThrowIfNull(worksheet, nameof(worksheet));
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            // Retrieve the selected relevant activities
            var relevantActivities = data.FormDocument.Attributes.GetAttributesWithPrefix(WellKnownFormDocumentAttibuteKeys.RelevantActivities).ToList();

            // Group the relevant activities
            var relevantActivityGroups = relevantActivities.GroupBy(a => a.Key.Split(WellKnownFormDocumentAttibuteKeys.RelevantActivities)[1].Split(".")[0]);

            foreach (var relevantActivityGroup in relevantActivityGroups)
            {
                // Check if the activity was selected
                var isSelected = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Selected);

                if (isSelected == "true")
                {
                    // Retrieve the name
                    var relevantActivity = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Label);

                    // Check the selected activity
                    string relevantActivityKey = BahamasReportHelperMethods.RetrieveRelevantActivityKey(relevantActivity);

                    // Retrieve the relevant activity data
                    var relevantActivityData = data.FormDocument.Attributes.GetAttributesWithPrefix(relevantActivityKey).ToList();

                    // Retrieve the created employees
                    var employees = relevantActivityData.GetAttributesWithPrefix(WellKnownFormDocumentAttibuteKeys.Employees).ToList();

                    // Group the employees
                    var employeeGroups = employees.GroupBy(a => a.Key.Split(relevantActivityKey + WellKnownFormDocumentAttibuteKeys.Employees)[1].Split(".")[0]);

                    foreach (var employeeGroup in employeeGroups)
                    {
                        // Retrieve the entity unique id
                        var entityUniqueId = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.EntityId);

                        SetCellValueAndStyle(worksheet, currentRow, 1, entityUniqueId);

                        // Retrieve the name
                        var activityName = relevantActivityGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Label);

                        SetCellValueAndStyle(worksheet, currentRow, 2, activityName);

                        var financialPeriodEndDate = data.FormDocument.Attributes.GetAttributeValue<string>(WellKnownFormDocumentAttibuteKeys.FinancialPeriodEndAt);

                        SetCellValueAndStyle(worksheet, currentRow, 3, financialPeriodEndDate);

                        // Retrieve the employee name
                        var employeeName = employeeGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Fullname);

                        SetCellValueAndStyle(worksheet, currentRow, 4, employeeName);

                        // Retrieve the employee qualification
                        var employeeQualification = employeeGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.Qualification);

                        SetCellValueAndStyle(worksheet, currentRow, 5, employeeQualification);

                        // Retrieve the employee years of experience
                        var yearsOfExperience = employeeGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.YearsOfExperience);

                        SetCellValueAndStyle(worksheet, currentRow, 6, yearsOfExperience);

                        // Retrieve the employee contract type
                        var contractType = employeeGroup.ToList().GetAttributeValueWithSuffix<string>(WellKnownFormDocumentAttibuteKeys.ContractType);

                        SetCellValueAndStyle(worksheet, currentRow, 7, contractType);

                        currentRow += 1;
                    }
                }
            }
        }
    }
}