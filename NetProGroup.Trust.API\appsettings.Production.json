{
  // Identifies the API to authenticate incoming authentication requests
  "AzureAd": {
    "Instance": "https://login.microsoftonline.com/",
    "TenantId": "4e469f54-cd7f-4db2-a879-55f2618bb8b3",
    "ClientId": "4712d602-0657-4462-989e-c8278e342aff",
    "AllowWebApiToBeAuthorizedByACL": true,
    "Audience": "api://4712d602-0657-4462-989e-c8278e342aff"
  },
  "AppRegistration": {
    "TenantId": "4e469f54-cd7f-4db2-a879-55f2618bb8b3",
    "ClientId": "4712d602-0657-4462-989e-c8278e342aff",
    "ClientSecret": "keyvault"
  },
  "ExternalId": {
    "ClientId": "73cfef3e-c370-4314-b7f6-44b188180c19",
    "TenantId": "056ac2cd-bd90-4496-9857-265f01797639"
  },
  "BlobStorage": {
    "AccountName": "saprdpcpweu",
    "ContainerName": "documents"
  },
  "Azure": {
    "MSGraph": {
      "AD": {
        // Use defaults from AppRegistration
        "Scopes": ".default"
      }
    }
  },
  "Smtp": {
    "host": "je-smtp-outbound-1.mimecast-offshore.com",
    "port": 587,
    "secure": true,
    "username": "<EMAIL>",
    "password": "keyvault",
    "fromEmail": "<EMAIL>",
    "nameEmail": "TridentTrust Client Portal"
  },
  // These settings are for the trust office
  "TrustOffice": {
    "EmailDomain": "tridenttrust.com",
    "ProductionOfficeEmailSuffix": "_BO_Dir_Change_Request",
    "ClientPortalUrl": "https://clientfilings.tridenttrust.com",
    "InvitationWhiteList": [

    ],
    "AllowedDomains": "tridenttrust.com",
    "SendInvitationToUserEnabled": false // false while deploying to PRD2. Set to true when creating release branch and deploying to PRD
  },
  "DataMigration": {
    "Enabled": false,
    "StoreUnprocessedRecords": false,
    "ActiveJurisdiction": "Bahamas",
    "CountryOverrides": {
      "KNA": "St. Kitts and Nevis"
    },
    "JurisdictionSettings": {
      "Bahamas": {
        "MongoConnectionString": "",
        "MongoDatabaseName": "",
        "Document": {
          "StorageAccountOptions": {
            "StorageAccounts": [
              {
                "Key": "SourceFileStorageBahamas",
                "AccountName": "",
                "DefaultContainer": ""
              }
            ]
          }
        }
      }
    }
  },
  "DataSync": {
    "Enabled": true,
    "JurisdictionCodes": [
      "BS" // Bahamas
    ]
  },
  "GoLive": {
    "GoliveDate": "2025-04-11",
    "SendQueuedInvitations": false // false while deploying to PRD2. Set to true when creating release branch and deploying to PRD
  },
  "FeatureFlags": {
    "Announcements": false
  }
}
