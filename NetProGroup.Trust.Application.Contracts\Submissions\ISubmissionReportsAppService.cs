﻿// <copyright file="ISubmissionReportsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Reports;

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// Interface for submission reporting operations within the application.
    /// </summary>
    public interface ISubmissionReportsAppService : IScopedService
    {
        /// <summary>
        /// Exports the submissions to a file.
        /// </summary>
        /// <param name="request">The request containing the parameters for the search.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains the submissions as a paged list.</returns>
        Task<ReportDownloadResponseDTO> ExportSubmissionForIRDAsync(ExportSubmissionDTO request);

        /// <summary>
        /// Generate the IRD bahamas submission report for the selected submissions (Management).
        /// </summary>
        /// <param name="request">The request with all the necessary data for the report.</param>
        /// <returns>The generated report as ReportDownloadResponseDTO.</returns>
        Task<ReportDownloadResponseDTO> ExportSubmissionsForIRDBahamasAsync(ITASubmissionRequestDTO request);

        /// <summary>
        /// Generate a submission data report for the selected submissions (Management).
        /// </summary>
        /// <param name="request">The request with all the necessary data for the report.</param>
        /// <returns>The generated report as ReportDownloadResponseDTO.</returns>
        Task<ReportDownloadResponseDTO> ExportSubmissionsForPanamaAsync(SubmissionDataRequestDTO request);


        /// <summary>
        /// Generate a report for the filtered Nevis submissions (Management).
        /// </summary>
        /// <param name="request">The request with all the parameters for the search.</param>
        /// <returns>The generated report as ReportDownloadResponseDTO.</returns>
        Task<ReportDownloadResponseDTO> GenerateSubmissionsReportForNevisAsync(SubmissionsReportRequestNevisDTO request);

        /// <summary>
        /// Generate a report for the filtered Bahamas submissions (Management).
        /// </summary>
        /// <param name="request">The request with all the parameters for the search.</param>
        /// <returns>The generated report as ReportDownloadResponseDTO.</returns>
        Task<ReportDownloadResponseDTO> GenerateSubmissionsReportForBahamasAsync(SubmissionsReportRequestBahamasDTO request);

        /// <summary>
        /// Generate a report for the filtered panama submissions (Management).
        /// </summary>
        /// <param name="request">The request with all the parameters for the search.</param>
        /// <returns>The generated report as ReportDownloadResponseDTO.</returns>
        Task<ReportDownloadResponseDTO> GenerateSubmissionsReportForPanamaAsync(SubmissionsReportRequestDTO request);
    }
}
